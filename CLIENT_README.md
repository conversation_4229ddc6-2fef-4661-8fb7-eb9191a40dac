# Polly API Client

A Python client library for interacting with the Polly API using the `requests` library. This client provides easy-to-use functions for user registration, poll management, voting, and retrieving poll results.

## Features

- 🔐 **User Registration**: Register new users via the `/register` endpoint
- 📊 **Paginated Poll Fetching**: Retrieve polls with pagination support from `/polls`
- 🗳️ **Vote Casting**: Cast votes on existing polls with authentication
- 📈 **Poll Results**: Retrieve detailed poll results with vote counts

## Installation

1. Install the required dependency:
```bash
pip install -r client_requirements.txt
```

2. Make sure your Polly API server is running (default: `http://localhost:8000`)

## Quick Start

```python
from polly_client import register_user, login_user, fetch_paginated_polls, cast_vote, get_poll_results

# Register a new user
user = register_user("john_doe", "secure_password")
print(f"User registered: {user['username']} (ID: {user['id']})")

# Login to get access token
token = login_user("john_doe", "secure_password")

# Fetch polls with pagination
polls = fetch_paginated_polls(skip=0, limit=10)
print(f"Found {len(polls)} polls")

# Cast a vote (requires authentication)
if polls and polls[0]['options']:
    poll_id = polls[0]['id']
    option_id = polls[0]['options'][0]['id']
    vote = cast_vote(poll_id, option_id, token)
    print(f"Vote cast successfully!")

# Get poll results
results = get_poll_results(poll_id)
print(f"Poll: {results['question']}")
for result in results['results']:
    print(f"  {result['text']}: {result['vote_count']} votes")
```

## API Functions

### `register_user(username, password, base_url="http://localhost:8000")`

Register a new user with the Polly API.

**Parameters:**
- `username` (str): Username for the new user
- `password` (str): Password for the new user  
- `base_url` (str, optional): Base URL of the API server

**Returns:**
- `Dict[str, Any]`: User information containing `id` and `username`

**Raises:**
- `PollyAPIError`: If registration fails (e.g., username already exists)

**Example:**
```python
user_data = register_user("alice", "my_secure_password")
# Returns: {"id": 1, "username": "alice"}
```

### `fetch_paginated_polls(skip=0, limit=10, base_url="http://localhost:8000")`

Fetch paginated poll data from the API.

**Parameters:**
- `skip` (int, optional): Number of items to skip (default: 0)
- `limit` (int, optional): Maximum number of items to return (default: 10)
- `base_url` (str, optional): Base URL of the API server

**Returns:**
- `List[Dict[str, Any]]`: List of poll objects with their options

**Example:**
```python
# Get first 5 polls
polls = fetch_paginated_polls(skip=0, limit=5)

# Get next 5 polls  
more_polls = fetch_paginated_polls(skip=5, limit=5)
```

### `cast_vote(poll_id, option_id, access_token, base_url="http://localhost:8000")`

Cast a vote on an existing poll.

**Parameters:**
- `poll_id` (int): ID of the poll to vote on
- `option_id` (int): ID of the option to vote for
- `access_token` (str): JWT access token for authentication
- `base_url` (str, optional): Base URL of the API server

**Returns:**
- `Dict[str, Any]`: Vote information containing `id`, `user_id`, `option_id`, and `created_at`

**Raises:**
- `PollyAPIError`: If voting fails (unauthorized, poll/option not found, etc.)

**Example:**
```python
vote_data = cast_vote(poll_id=1, option_id=2, access_token="your_jwt_token")
# Returns: {"id": 1, "user_id": 1, "option_id": 2, "created_at": "2024-01-01T12:00:00Z"}
```

### `get_poll_results(poll_id, base_url="http://localhost:8000")`

Retrieve poll results for a specific poll.

**Parameters:**
- `poll_id` (int): ID of the poll to get results for
- `base_url` (str, optional): Base URL of the API server

**Returns:**
- `Dict[str, Any]`: Poll results with vote counts for each option

**Example:**
```python
results = get_poll_results(poll_id=1)
# Returns: {
#     "poll_id": 1,
#     "question": "What's your favorite color?",
#     "results": [
#         {"option_id": 1, "text": "Red", "vote_count": 5},
#         {"option_id": 2, "text": "Blue", "vote_count": 3}
#     ]
# }
```

### `login_user(username, password, base_url="http://localhost:8000")`

Login user and return access token (convenience function).

**Parameters:**
- `username` (str): Username
- `password` (str): Password
- `base_url` (str, optional): Base URL of the API server

**Returns:**
- `str`: JWT access token

## Error Handling

All functions raise `PollyAPIError` for API-related errors. This custom exception includes:

- `message`: Error description
- `status_code`: HTTP status code (if available)
- `response_data`: Raw response data (if available)

```python
from polly_client import PollyAPIError

try:
    user = register_user("existing_user", "password")
except PollyAPIError as e:
    print(f"Error: {e.message}")
    if e.status_code == 400:
        print("Username already exists!")
```

## Advanced Usage

### Using the PollyClient Class

For more control, you can use the `PollyClient` class directly:

```python
from polly_client import PollyClient

client = PollyClient("http://localhost:8000", timeout=60)
client.set_access_token("your_jwt_token")

# Make custom requests
response = client._make_request("GET", "/polls/1")
poll_data = response.json()
```

### Pagination Example

```python
def fetch_all_polls(base_url="http://localhost:8000", page_size=10):
    """Fetch all polls using pagination."""
    all_polls = []
    skip = 0
    
    while True:
        polls = fetch_paginated_polls(skip=skip, limit=page_size, base_url=base_url)
        if not polls:
            break
        
        all_polls.extend(polls)
        skip += page_size
        
        # If we got fewer polls than requested, we've reached the end
        if len(polls) < page_size:
            break
    
    return all_polls
```

## Running the Examples

1. **Basic Example:**
```bash
python example_usage.py
```

2. **Run Tests:**
```bash
python test_client.py
```

## Requirements

- Python 3.7+
- `requests` library (see `client_requirements.txt`)
- Running Polly API server

## API Endpoints Used

- `POST /register` - User registration
- `POST /login` - User authentication  
- `GET /polls` - Fetch polls with pagination
- `POST /polls/{poll_id}/vote` - Cast vote (requires authentication)
- `GET /polls/{poll_id}/results` - Get poll results

## Error Codes

- `400`: Bad request (e.g., username already exists, invalid data)
- `401`: Unauthorized (invalid or missing authentication token)
- `404`: Not found (poll or option doesn't exist)
- `500`: Internal server error

## Contributing

Feel free to submit issues and enhancement requests!
