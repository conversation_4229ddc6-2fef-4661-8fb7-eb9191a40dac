{".class": "MypyFile", "_fullname": "polly_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PollyAPIError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "polly_client.PollyAPIError", "name": "PollyAPIError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "polly_client.PollyAPIError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "polly_client", "mro": ["polly_client.PollyAPIError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "status_code", "response_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.PollyAPIError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "status_code", "response_data"], "arg_types": ["polly_client.PollyAPIError", "builtins.str", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PollyAPIError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyAPIError.message", "name": "message", "type": "builtins.str"}}, "response_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyAPIError.response_data", "name": "response_data", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyAPIError.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "polly_client.PollyAPIError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "polly_client.PollyAPIError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PollyClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "polly_client.PollyClient", "name": "PollyClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "polly_client.PollyClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "polly_client", "mro": ["polly_client.PollyClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "base_url", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.PollyClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "base_url", "timeout"], "arg_types": ["polly_client.PollyClient", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PollyClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "endpoint", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.PollyClient._make_request", "name": "_make_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "endpoint", "kwargs"], "arg_types": ["polly_client.PollyClient", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_request of PollyClient", "ret_type": {".class": "AnyType", "missing_import_name": "polly_client.requests", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyClient.access_token", "name": "access_token", "type": {".class": "NoneType"}}}, "base_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyClient.base_url", "name": "base_url", "type": "builtins.str"}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyClient.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": "polly_client.requests", "source_any": {".class": "AnyType", "missing_import_name": "polly_client.requests", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "set_access_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.PollyClient.set_access_token", "name": "set_access_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["polly_client.PollyClient", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_access_token of PollyClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.PollyClient.timeout", "name": "timeout", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "polly_client.PollyClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "polly_client.PollyClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "polly_client.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast_vote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["poll_id", "option_id", "access_token", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.cast_vote", "name": "cast_vote", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["poll_id", "option_id", "access_token", "base_url"], "arg_types": ["builtins.int", "builtins.int", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cast_vote", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "polly_client.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "fetch_paginated_polls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["skip", "limit", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.fetch_paginated_polls", "name": "fetch_paginated_polls", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["skip", "limit", "base_url"], "arg_types": ["builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_paginated_polls", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_poll_results": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["poll_id", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.get_poll_results", "name": "get_poll_results", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["poll_id", "base_url"], "arg_types": ["builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_poll_results", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "login_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["username", "password", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.login_user", "name": "login_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["username", "password", "base_url"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "login_user", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.option_id", "name": "option_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "poll_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.poll_id", "name": "poll_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "polls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.polls", "name": "polls", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "register_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["username", "password", "base_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "polly_client.register_user", "name": "register_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["username", "password", "base_url"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_user", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "polly_client.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "polly_client.requests", "source_any": null, "type_of_any": 3}}}, "results": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.results", "name": "results", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.token", "name": "token", "type": "builtins.str"}}, "user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.user", "name": "user", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "vote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "polly_client.vote", "name": "vote", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "/root/GitHub/AI-For-Devs-II/Polly-API/polly_client.py"}