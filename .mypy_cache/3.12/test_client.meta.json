{"data_mtime": 1757701355, "dep_lines": [10, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "unittest", "json", "polly_client", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing", "unittest.case", "unittest.loader", "unittest.main"], "hash": "681b2f38fe0d31c540d2d83f6e339bd1bdfed930", "id": "test_client", "ignore_all": false, "interface_hash": "08e3ace8d6bd80e1fe6e106580571e496ebe734d", "mtime": 1757701322, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/AI-For-Devs-II/Polly-API/test_client.py", "plugin_data": null, "size": 10111, "suppressed": [], "version_id": "1.15.0"}