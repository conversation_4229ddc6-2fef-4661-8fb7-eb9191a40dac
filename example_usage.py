#!/usr/bin/env python3
"""
Example usage of the Polly API client functions.

This script demonstrates how to use the client functions to:
1. Register a new user
2. Login and get an access token
3. Fetch paginated poll data
4. Cast votes on polls
5. Retrieve poll results
"""

from polly_client import (
    register_user, 
    login_user, 
    fetch_paginated_polls, 
    cast_vote, 
    get_poll_results,
    PollyAPIError
)
import time
import random


def main():
    """Main function demonstrating the Polly API client usage."""
    
    # Configuration
    BASE_URL = "http://localhost:8000"
    
    # Generate a unique username for this example
    timestamp = int(time.time())
    username = f"demo_user_{timestamp}"
    password = "demo_password_123"
    
    print("🗳️  Polly API Client Demo")
    print("=" * 50)
    
    try:
        # Step 1: Register a new user
        print(f"\n1️⃣  Registering user '{username}'...")
        user_data = register_user(username, password, BASE_URL)
        print(f"✅ User registered successfully!")
        print(f"   User ID: {user_data['id']}")
        print(f"   Username: {user_data['username']}")
        
        # Step 2: Login to get access token
        print(f"\n2️⃣  Logging in user '{username}'...")
        access_token = login_user(username, password, BASE_URL)
        print("✅ Login successful! Access token obtained.")
        
        # Step 3: Fetch paginated poll data
        print("\n3️⃣  Fetching paginated poll data...")
        polls = fetch_paginated_polls(skip=0, limit=10, base_url=BASE_URL)
        print(f"✅ Found {len(polls)} polls")
        
        if not polls:
            print("ℹ️  No polls found. The demo will create some sample data if you have admin access.")
            return
        
        # Display poll information
        for i, poll in enumerate(polls, 1):
            print(f"\n   Poll {i}:")
            print(f"   📊 ID: {poll['id']}")
            print(f"   ❓ Question: {poll['question']}")
            print(f"   📅 Created: {poll['created_at']}")
            print(f"   👤 Owner ID: {poll['owner_id']}")
            print(f"   🔢 Options: {len(poll['options'])}")
            
            for j, option in enumerate(poll['options'], 1):
                print(f"      {j}. {option['text']} (ID: {option['id']})")
        
        # Step 4: Cast a vote on the first poll
        if polls and polls[0]['options']:
            first_poll = polls[0]
            poll_id = first_poll['id']
            
            # Choose a random option to vote for
            random_option = random.choice(first_poll['options'])
            option_id = random_option['id']
            
            print(f"\n4️⃣  Casting vote on poll '{first_poll['question']}'...")
            print(f"   Voting for option: '{random_option['text']}'")
            
            vote_data = cast_vote(poll_id, option_id, access_token, BASE_URL)
            print("✅ Vote cast successfully!")
            print(f"   Vote ID: {vote_data['id']}")
            print(f"   User ID: {vote_data['user_id']}")
            print(f"   Option ID: {vote_data['option_id']}")
            print(f"   Voted at: {vote_data['created_at']}")
            
            # Step 5: Retrieve poll results
            print(f"\n5️⃣  Retrieving results for poll {poll_id}...")
            results = get_poll_results(poll_id, BASE_URL)
            print("✅ Poll results retrieved!")
            print(f"   Poll ID: {results['poll_id']}")
            print(f"   Question: {results['question']}")
            print("   📊 Results:")
            
            total_votes = sum(result['vote_count'] for result in results['results'])
            
            for result in results['results']:
                vote_count = result['vote_count']
                percentage = (vote_count / total_votes * 100) if total_votes > 0 else 0
                print(f"      • {result['text']}: {vote_count} votes ({percentage:.1f}%)")
            
            print(f"   Total votes: {total_votes}")
        
        print("\n🎉 Demo completed successfully!")
        
    except PollyAPIError as e:
        print(f"\n❌ Polly API Error: {e.message}")
        if e.status_code:
            print(f"   Status Code: {e.status_code}")
        if e.response_data:
            print(f"   Response Data: {e.response_data}")
            
        # Provide helpful error messages
        if e.status_code == 400 and "already registered" in e.message:
            print("\n💡 Tip: The username is already taken. Try running the script again with a different username.")
        elif e.status_code == 401:
            print("\n💡 Tip: Authentication failed. Check your credentials or token.")
        elif e.status_code == 404:
            print("\n💡 Tip: The requested resource was not found. Check the poll/option IDs.")
            
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("💡 Make sure the Polly API server is running on http://localhost:8000")


def demo_pagination():
    """Demonstrate pagination functionality."""
    print("\n📄 Pagination Demo")
    print("-" * 30)
    
    BASE_URL = "http://localhost:8000"
    page_size = 5
    
    try:
        # Fetch first page
        print(f"Fetching first {page_size} polls...")
        first_page = fetch_paginated_polls(skip=0, limit=page_size, base_url=BASE_URL)
        print(f"First page: {len(first_page)} polls")
        
        # Fetch second page
        print(f"Fetching next {page_size} polls...")
        second_page = fetch_paginated_polls(skip=page_size, limit=page_size, base_url=BASE_URL)
        print(f"Second page: {len(second_page)} polls")
        
        # Show total unique polls
        all_poll_ids = set()
        for poll in first_page + second_page:
            all_poll_ids.add(poll['id'])
        
        print(f"Total unique polls across both pages: {len(all_poll_ids)}")
        
    except PollyAPIError as e:
        print(f"Pagination demo failed: {e.message}")


if __name__ == "__main__":
    main()
    
    # Uncomment the line below to also run the pagination demo
    # demo_pagination()
