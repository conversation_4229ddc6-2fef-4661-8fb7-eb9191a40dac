{".class": "MypyFile", "_fullname": "warnings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WarningMessage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "warnings.WarningMessage", "name": "WarningMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "warnings.WarningMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "warnings", "mro": ["warnings.WarningMessage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "category", "filename", "lineno", "file", "line", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.WarningMessage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "category", "filename", "lineno", "file", "line", "source"], "arg_types": ["warnings.WarningMessage", {".class": "UnionType", "items": ["builtins.Warning", "builtins.str"], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WarningMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.category", "name": "category", "type": {".class": "TypeType", "item": "builtins.Warning"}}}, "file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.file", "name": "file", "type": {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.filename", "name": "filename", "type": "builtins.str"}}, "line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.line", "name": "line", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.lineno", "name": "lineno", "type": "builtins.int"}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.message", "name": "message", "type": {".class": "UnionType", "items": ["builtins.Warning", "builtins.str"], "uses_pep604_syntax": true}}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "warnings.WarningMessage.source", "name": "source", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings.WarningMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "warnings.WarningMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ActionKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "warnings._ActionKind", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "once"}], "uses_pep604_syntax": false}}}, "_OptionError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "warnings._OptionError", "name": "_OptionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "warnings._OptionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "warnings", "mro": ["warnings._OptionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._OptionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "warnings._OptionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_W": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "name": "_W", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "warnings.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "catch_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "warnings.catch_warnings", "name": "catch_warnings", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "id": 1, "name": "_W", "namespace": "warnings.catch_warnings", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "warnings.catch_warnings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "warnings", "mro": ["warnings.catch_warnings", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.catch_warnings.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "id": 1, "name": "_W", "namespace": "warnings.catch_warnings", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of catch_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "id": 1, "name": "_W", "namespace": "warnings.catch_warnings", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.catch_warnings.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "id": 1, "name": "_W", "namespace": "warnings.catch_warnings", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "warnings.catch_warnings.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, "builtins.bool", {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "warnings.catch_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, "builtins.bool", {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "record", "module", "action", "category", "lineno", "append"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, "builtins.bool", {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings.catch_warnings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "warnings._W", "id": 1, "name": "_W", "namespace": "warnings.catch_warnings", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_W"], "typeddict_type": null}}, "filters": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "warnings.filters", "name": "filters", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "filterwarnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["action", "message", "category", "module", "lineno", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.filterwarnings", "name": "filterwarnings", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["action", "message", "category", "module", "lineno", "append"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, "builtins.str", {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filterwarnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatwarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["message", "category", "filename", "lineno", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.formatwarning", "name": "formatwarning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["message", "category", "filename", "lineno", "line"], "arg_types": [{".class": "UnionType", "items": ["builtins.Warning", "builtins.str"], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatwarning", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "resetwarnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.resetwarnings", "name": "resetwarnings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resetwarnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "showwarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["message", "category", "filename", "lineno", "file", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.showwarning", "name": "showwarning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["message", "category", "filename", "lineno", "file", "line"], "arg_types": [{".class": "UnionType", "items": ["builtins.Warning", "builtins.str"], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showwarning", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "simplefilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["action", "category", "lineno", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "warnings.simplefilter", "name": "simplefilter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["action", "category", "lineno", "append"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "warnings._ActionKind"}, {".class": "TypeType", "item": "builtins.Warning"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simplefilter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "warn_explicit": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn_explicit", "kind": "Gdef"}}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/warnings.pyi"}