{".class": "MypyFile", "_fullname": "concurrent.futures._base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALL_COMPLETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "ALL_COMPLETED", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.ALL_COMPLETED", "name": "ALL_COMPLETED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ALL_COMPLETED"}, "type_ref": "builtins.str"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BrokenExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.BrokenExecutor", "name": "BrokenExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.BrokenExecutor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.BrokenExecutor", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.BrokenExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.BrokenExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CANCELLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "CANCELLED", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.CANCELLED", "name": "CANCELLED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CANCELLED"}, "type_ref": "builtins.str"}}}, "CANCELLED_AND_NOTIFIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "CANCELLED_AND_NOTIFIED", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.CANCELLED_AND_NOTIFIED", "name": "CANCELLED_AND_NOTIFIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CANCELLED_AND_NOTIFIED"}, "type_ref": "builtins.str"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CancelledError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.CancelledError", "name": "CancelledError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.CancelledError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.CancelledError", "concurrent.futures._base.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.CancelledError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.CancelledError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DoneAndNotDoneFutures": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.DoneAndNotDoneFutures", "name": "DoneAndNotDoneFutures", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["done", "not_done"]}}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.DoneAndNotDoneFutures", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "done"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "not_done"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "done", "not_done"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "done", "not_done"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of DoneAndNotDoneFutures", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of DoneAndNotDoneFutures", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DoneAndNotDoneFutures", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DoneAndNotDoneFutures", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "done", "not_done"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "done", "not_done"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of DoneAndNotDoneFutures", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._NT", "id": -1, "name": "_NT", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures._source", "name": "_source", "type": "builtins.str"}}, "done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.done", "name": "done", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "done-redefinition": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.DoneAndNotDoneFutures.done", "kind": "<PERSON><PERSON><PERSON>"}, "not_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.not_done", "name": "not_done", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "not_done-redefinition": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.DoneAndNotDoneFutures.not_done", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.DoneAndNotDoneFutures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.DoneAndNotDoneFutures"}}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.DoneAndNotDoneFutures", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": ["_T"], "typeddict_type": null}}, "Error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.Error", "name": "Error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Executor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.Executor", "name": "Executor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.Executor", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Executor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.Executor", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of Executor", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Executor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.Executor", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Executor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.Executor", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["concurrent.futures._base.Executor", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of Executor", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": ["self", "fn", "iterables", "timeout", "chunksize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": ["self", "fn", "iterables", "timeout", "chunksize"], "arg_types": ["concurrent.futures._base.Executor", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.Executor.map", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of Executor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.Executor.map", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.Executor.map", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "wait", "cancel_futures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "wait", "cancel_futures"], "arg_types": ["concurrent.futures._base.Executor", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of Executor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "submit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Executor.submit", "name": "submit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": ["concurrent.futures._base.Executor", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "concurrent.futures._base._P", "id": -1, "name": "_P", "namespace": "concurrent.futures._base.Executor.submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "concurrent.futures._base._P", "id": -1, "name": "_P", "namespace": "concurrent.futures._base.Executor.submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -2, "name": "_T", "namespace": "concurrent.futures._base.Executor.submit", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "concurrent.futures._base._P", "id": -1, "name": "_P", "namespace": "concurrent.futures._base.Executor.submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "concurrent.futures._base._P", "id": -1, "name": "_P", "namespace": "concurrent.futures._base.Executor.submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "submit of Executor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -2, "name": "_T", "namespace": "concurrent.futures._base.Executor.submit", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "concurrent.futures._base._P", "id": -1, "name": "_P", "namespace": "concurrent.futures._base.Executor.submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -2, "name": "_T", "namespace": "concurrent.futures._base.Executor.submit", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Executor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.Executor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FINISHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "FINISHED", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.FINISHED", "name": "FINISHED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "FINISHED"}, "type_ref": "builtins.str"}}}, "FIRST_COMPLETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "FIRST_COMPLETED", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.FIRST_COMPLETED", "name": "FIRST_COMPLETED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "FIRST_COMPLETED"}, "type_ref": "builtins.str"}}}, "FIRST_EXCEPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "FIRST_EXCEPTION", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.FIRST_EXCEPTION", "name": "FIRST_EXCEPTION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "FIRST_EXCEPTION"}, "type_ref": "builtins.str"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Future": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.Future", "name": "Future", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.Future", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "concurrent.futures._base.Future.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of Future", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.Future._condition", "name": "_condition", "type": "threading.Condition"}}, "_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.Future._exception", "name": "_exception", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.Future._result", "name": "_result", "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.Future._state", "name": "_state", "type": "builtins.str"}}, "_waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base.Future._waiters", "name": "_waiters", "type": {".class": "Instance", "args": ["concurrent.futures._base._Waiter"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_done_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.add_done_callback", "name": "add_done_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_done_callback of Future", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel of Future", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancelled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.cancelled", "name": "cancelled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancelled of Future", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.done", "name": "done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "done of Future", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.exception", "name": "exception", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exception of Future", "ret_type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.result", "name": "result", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "result of Future", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.running", "name": "running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "running of Future", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.set_exception", "name": "set_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_exception of Future", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.set_result", "name": "set_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_result of Future", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_running_or_notify_cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.Future.set_running_or_notify_cancel", "name": "set_running_or_notify_cancel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_running_or_notify_cancel of Future", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.Future.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": 1, "name": "_T", "namespace": "concurrent.futures._base.Future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InvalidStateError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base.InvalidStateError", "name": "InvalidStateError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.InvalidStateError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base.InvalidStateError", "concurrent.futures._base.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base.InvalidStateError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base.InvalidStateError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.LOGGER", "name": "LOGGER", "type": "logging.Logger"}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PENDING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "PENDING", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.PENDING", "name": "PENDING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PENDING"}, "type_ref": "builtins.str"}}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RUNNING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "RUNNING", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "concurrent.futures._base.RUNNING", "name": "RUNNING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "RUNNING"}, "type_ref": "builtins.str"}}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "builtins.TimeoutError", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AcquireFutures": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._AcquireFutures", "name": "_AcquireFutures", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AcquireFutures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._AcquireFutures", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AcquireFutures.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["concurrent.futures._base._AcquireFutures"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _AcquireFutures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AcquireFutures.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["concurrent.futures._base._AcquireFutures", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of _AcquireFutures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "futures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AcquireFutures.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "futures"], "arg_types": ["concurrent.futures._base._AcquireFutures", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AcquireFutures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._AcquireFutures.futures", "name": "futures", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._AcquireFutures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base._AcquireFutures", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AllCompletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base._Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._AllCompletedWaiter", "name": "_AllCompleted<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AllCompletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._AllCompletedWaiter", "concurrent.futures._base._Waiter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "num_pending_calls", "stop_on_exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AllCompletedWaiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "num_pending_calls", "stop_on_exception"], "arg_types": ["concurrent.futures._base._AllCompletedWaiter", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AllCompleted<PERSON>aiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._AllCompletedWaiter.lock", "name": "lock", "type": "_thread.LockType"}}, "num_pending_calls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._AllCompletedWaiter.num_pending_calls", "name": "num_pending_calls", "type": "builtins.int"}}, "stop_on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._AllCompletedWaiter.stop_on_exception", "name": "stop_on_exception", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._AllCompletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base._AllCompletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AsCompletedFuture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_condition", 1], ["_state", 1], ["_waiters", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._AsCompletedFuture", "name": "_AsCompletedFuture", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T_co", "id": 1, "name": "_T_co", "namespace": "concurrent.futures._base._AsCompletedFuture", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "concurrent.futures._base._AsCompletedFuture", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._AsCompletedFuture", "builtins.object"], "names": {".class": "SymbolTable", "_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "concurrent.futures._base._AsCompletedFuture._condition", "name": "_condition", "type": "threading.Condition"}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "concurrent.futures._base._AsCompletedFuture._state", "name": "_state", "type": "builtins.str"}}, "_waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "concurrent.futures._base._AsCompletedFuture._waiters", "name": "_waiters", "type": {".class": "Instance", "args": ["concurrent.futures._base._Waiter"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AsCompletedFuture.result", "name": "result", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T_co", "id": 1, "name": "_T_co", "namespace": "concurrent.futures._base._AsCompletedFuture", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "concurrent.futures._base._AsCompletedFuture"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "result of _AsCompletedFuture", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T_co", "id": 1, "name": "_T_co", "namespace": "concurrent.futures._base._AsCompletedFuture", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._AsCompletedFuture.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T_co", "id": 1, "name": "_T_co", "namespace": "concurrent.futures._base._AsCompletedFuture", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "concurrent.futures._base._AsCompletedFuture"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_AsCompletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base._Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._AsCompletedWaiter", "name": "_AsCompleted<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._AsCompletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._AsCompletedWaiter", "concurrent.futures._base._Waiter", "builtins.object"], "names": {".class": "SymbolTable", "lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._AsCompletedWaiter.lock", "name": "lock", "type": "_thread.LockType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._AsCompletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base._AsCompletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FUTURE_STATES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base._FUTURE_STATES", "name": "_FUTURE_STATES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_FirstCompletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base._Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._FirstCompletedWaiter", "name": "_FirstCompleted<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._FirstCompletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._FirstCompletedWaiter", "concurrent.futures._base._Waiter", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._FirstCompletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base._FirstCompletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_STATE_TO_DESCRIPTION_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base._STATE_TO_DESCRIPTION_MAP", "name": "_STATE_TO_DESCRIPTION_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_Waiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures._base._Waiter", "name": "_Waiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._Waiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures._base", "mro": ["concurrent.futures._base._Waiter", "builtins.object"], "names": {".class": "SymbolTable", "add_cancelled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._Waiter.add_cancelled", "name": "add_cancelled", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["concurrent.futures._base._Waiter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cancelled of _Waiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._Waiter.add_exception", "name": "add_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["concurrent.futures._base._Waiter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_exception of _Waiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base._Waiter.add_result", "name": "add_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["concurrent.futures._base._Waiter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_result of _Waiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._Waiter.event", "name": "event", "type": "threading.Event"}}, "finished_futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures._base._Waiter.finished_futures", "name": "finished_futures", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._Waiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures._base._Waiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures._base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_completed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["fs", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.as_completed", "name": "as_completed", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["fs", "timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base._AsCompletedFuture"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_completed", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_hidden": true, "module_public": false}, "wait": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["fs", "timeout", "return_when"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures._base.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "concurrent.futures._base.DoneAndNotDoneFutures"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures._base._T", "id": -1, "name": "_T", "namespace": "concurrent.futures._base.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/concurrent/futures/_base.pyi"}