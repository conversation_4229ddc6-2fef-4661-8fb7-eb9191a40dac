{".class": "MypyFile", "_fullname": "example_usage", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PollyAPIError": {".class": "SymbolTableNode", "cross_ref": "polly_client.PollyAPIError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "example_usage.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast_vote": {".class": "SymbolTableNode", "cross_ref": "polly_client.cast_vote", "kind": "Gdef"}, "demo_pagination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "example_usage.demo_pagination", "name": "demo_pagination", "type": null}}, "fetch_paginated_polls": {".class": "SymbolTableNode", "cross_ref": "polly_client.fetch_paginated_polls", "kind": "Gdef"}, "get_poll_results": {".class": "SymbolTableNode", "cross_ref": "polly_client.get_poll_results", "kind": "Gdef"}, "login_user": {".class": "SymbolTableNode", "cross_ref": "polly_client.login_user", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "example_usage.main", "name": "main", "type": null}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "register_user": {".class": "SymbolTableNode", "cross_ref": "polly_client.register_user", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "/root/GitHub/AI-For-Devs-II/Polly-API/example_usage.py"}