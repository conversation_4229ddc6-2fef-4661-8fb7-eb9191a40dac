{".class": "MypyFile", "_fullname": "asyncio.mixins", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Never": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_LoopBoundMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.mixins._LoopBoundMixin", "name": "_LoopBoundMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.mixins._LoopBoundMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.mixins", "mro": ["asyncio.mixins._LoopBoundMixin", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.mixins._LoopBoundMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.mixins._LoopBoundMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_global_lock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.mixins._global_lock", "name": "_global_lock", "type": "_thread.LockType"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/asyncio/mixins.pyi"}