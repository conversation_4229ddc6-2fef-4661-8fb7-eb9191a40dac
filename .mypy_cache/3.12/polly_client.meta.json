{"data_mtime": 1757701297, "dep_lines": [12, 13, 1, 1, 1, 1, 11], "dep_prios": [5, 5, 5, 30, 30, 30, 10], "dependencies": ["typing", "datetime", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "cae15d0a9b38000b8b6218b2e95018aac4d9496e", "id": "polly_client", "ignore_all": true, "interface_hash": "5be7f82785bab0474afc51e195bb90c664a07dda", "mtime": 1757701234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/AI-For-Devs-II/Polly-API/polly_client.py", "plugin_data": null, "size": 9567, "suppressed": ["requests"], "version_id": "1.15.0"}