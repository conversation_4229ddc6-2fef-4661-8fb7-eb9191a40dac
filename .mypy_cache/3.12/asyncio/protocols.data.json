{".class": "MypyFile", "_fullname": "asyncio.protocols", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.protocols.BaseProtocol", "name": "BaseProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BaseProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.protocols", "mro": ["asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BaseProtocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["asyncio.protocols.BaseProtocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of BaseProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BaseProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["asyncio.protocols.BaseProtocol", "asyncio.transports.BaseTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of BaseProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pause_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BaseProtocol.pause_writing", "name": "pause_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.protocols.BaseProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause_writing of BaseProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resume_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BaseProtocol.resume_writing", "name": "resume_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.protocols.BaseProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resume_writing of BaseProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.protocols.BaseProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.protocols.BaseProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BufferedProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.BaseProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.protocols.BufferedProtocol", "name": "BufferedProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BufferedProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.protocols", "mro": ["asyncio.protocols.BufferedProtocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "buffer_updated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nbytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BufferedProtocol.buffer_updated", "name": "buffer_updated", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nbytes"], "arg_types": ["asyncio.protocols.BufferedProtocol", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buffer_updated of BufferedProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BufferedProtocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.protocols.BufferedProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of BufferedProtocol", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.BufferedProtocol.get_buffer", "name": "get_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": ["asyncio.protocols.BufferedProtocol", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_buffer of BufferedProtocol", "ret_type": "_collections_abc.<PERSON><PERSON>er", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.protocols.BufferedProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.protocols.BufferedProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DatagramProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.BaseProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.protocols.DatagramProtocol", "name": "DatagramProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.protocols.DatagramProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.protocols", "mro": ["asyncio.protocols.DatagramProtocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.DatagramProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["asyncio.protocols.DatagramProtocol", "asyncio.transports.DatagramTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of DatagramProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datagram_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.DatagramProtocol.datagram_received", "name": "datagram_received", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "addr"], "arg_types": ["asyncio.protocols.DatagramProtocol", "builtins.bytes", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "datagram_received of DatagramProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.DatagramProtocol.error_received", "name": "error_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["asyncio.protocols.DatagramProtocol", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error_received of DatagramProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.protocols.DatagramProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.protocols.DatagramProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.BaseProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.protocols.Protocol", "name": "Protocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.protocols.Protocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.protocols", "mro": ["asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.Protocol.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["asyncio.protocols.Protocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.Protocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.protocols.Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of Protocol", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.protocols.Protocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.protocols.Protocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SubprocessProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.BaseProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.protocols.SubprocessProtocol", "name": "SubprocessProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.protocols.SubprocessProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.protocols", "mro": ["asyncio.protocols.SubprocessProtocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "pipe_connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.SubprocessProtocol.pipe_connection_lost", "name": "pipe_connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "exc"], "arg_types": ["asyncio.protocols.SubprocessProtocol", "builtins.int", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe_connection_lost of SubprocessProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pipe_data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.SubprocessProtocol.pipe_data_received", "name": "pipe_data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "data"], "arg_types": ["asyncio.protocols.SubprocessProtocol", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipe_data_received of SubprocessProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_exited": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.protocols.SubprocessProtocol.process_exited", "name": "process_exited", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.protocols.SubprocessProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_exited of SubprocessProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.protocols.SubprocessProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.protocols.SubprocessProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.protocols.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.protocols.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "transports": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/asyncio/protocols.pyi"}