"""
Polly API Client - Python client functions for the Polly API using requests library.

This module provides client-side functions to interact with the Polly API endpoints:
- User registration
- Fetching paginated poll data
- Casting votes on polls
- Retrieving poll results
"""

import requests
from typing import Dict, List, Optional, Any
from datetime import datetime


class Polly<PERSON>IError(Exception):
    """Custom exception for Polly API errors."""
    def __init__(self, message: str, status_code: int = None, response_data: Dict = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class PollyClient:
    """Client class for interacting with the Polly API."""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        """
        Initialize the Polly API client.
        
        Args:
            base_url: Base URL of the Polly API server
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.access_token = None
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an HTTP request to the API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            PollyAPIError: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        
        # Add authorization header if token is available
        if self.access_token:
            headers = kwargs.get('headers', {})
            headers['Authorization'] = f"Bearer {self.access_token}"
            kwargs['headers'] = headers
        
        # Set default timeout
        kwargs.setdefault('timeout', self.timeout)
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Check for HTTP errors
            if not response.ok:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                error_message = error_data.get('detail', f"HTTP {response.status_code} error")
                raise PollyAPIError(
                    message=error_message,
                    status_code=response.status_code,
                    response_data=error_data
                )
            
            return response
            
        except requests.exceptions.RequestException as e:
            raise PollyAPIError(f"Request failed: {str(e)}")
    
    def set_access_token(self, token: str):
        """Set the access token for authenticated requests."""
        self.access_token = token


def register_user(username: str, password: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    Register a new user via the /register endpoint.
    
    Args:
        username: Username for the new user
        password: Password for the new user
        base_url: Base URL of the Polly API server
        
    Returns:
        Dictionary containing user information (id, username)
        
    Raises:
        PollyAPIError: If registration fails
        
    Example:
        >>> user_data = register_user("john_doe", "secure_password123")
        >>> print(f"User registered with ID: {user_data['id']}")
    """
    client = PollyClient(base_url)
    
    user_data = {
        "username": username,
        "password": password
    }
    
    response = client._make_request(
        method="POST",
        endpoint="/register",
        json=user_data,
        headers={"Content-Type": "application/json"}
    )
    
    return response.json()


def fetch_paginated_polls(skip: int = 0, limit: int = 10, base_url: str = "http://localhost:8000") -> List[Dict[str, Any]]:
    """
    Fetch paginated poll data from /polls endpoint.
    
    Args:
        skip: Number of items to skip (default: 0)
        limit: Maximum number of items to return (default: 10)
        base_url: Base URL of the Polly API server
        
    Returns:
        List of poll dictionaries containing poll information
        
    Raises:
        PollyAPIError: If the request fails
        
    Example:
        >>> polls = fetch_paginated_polls(skip=0, limit=5)
        >>> for poll in polls:
        ...     print(f"Poll: {poll['question']} (ID: {poll['id']})")
    """
    client = PollyClient(base_url)
    
    params = {
        "skip": skip,
        "limit": limit
    }
    
    response = client._make_request(
        method="GET",
        endpoint="/polls",
        params=params
    )
    
    return response.json()


def cast_vote(poll_id: int, option_id: int, access_token: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    Cast a vote on an existing poll.
    
    Args:
        poll_id: ID of the poll to vote on
        option_id: ID of the option to vote for
        access_token: JWT access token for authentication
        base_url: Base URL of the Polly API server
        
    Returns:
        Dictionary containing vote information (id, user_id, option_id, created_at)
        
    Raises:
        PollyAPIError: If voting fails (unauthorized, poll/option not found, etc.)
        
    Example:
        >>> vote_data = cast_vote(poll_id=1, option_id=2, access_token="your_jwt_token")
        >>> print(f"Vote cast successfully! Vote ID: {vote_data['id']}")
    """
    client = PollyClient(base_url)
    client.set_access_token(access_token)
    
    vote_data = {
        "option_id": option_id
    }
    
    response = client._make_request(
        method="POST",
        endpoint=f"/polls/{poll_id}/vote",
        json=vote_data,
        headers={"Content-Type": "application/json"}
    )
    
    return response.json()


def get_poll_results(poll_id: int, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    Retrieve poll results for a specific poll.
    
    Args:
        poll_id: ID of the poll to get results for
        base_url: Base URL of the Polly API server
        
    Returns:
        Dictionary containing poll results with structure:
        {
            "poll_id": int,
            "question": str,
            "results": [
                {
                    "option_id": int,
                    "text": str,
                    "vote_count": int
                },
                ...
            ]
        }
        
    Raises:
        PollyAPIError: If the poll is not found or request fails
        
    Example:
        >>> results = get_poll_results(poll_id=1)
        >>> print(f"Poll: {results['question']}")
        >>> for result in results['results']:
        ...     print(f"  {result['text']}: {result['vote_count']} votes")
    """
    client = PollyClient(base_url)
    
    response = client._make_request(
        method="GET",
        endpoint=f"/polls/{poll_id}/results"
    )
    
    return response.json()


# Convenience functions for easier usage
def login_user(username: str, password: str, base_url: str = "http://localhost:8000") -> str:
    """
    Login user and return access token.
    
    Args:
        username: Username
        password: Password
        base_url: Base URL of the Polly API server
        
    Returns:
        JWT access token string
        
    Raises:
        PollyAPIError: If login fails
    """
    client = PollyClient(base_url)
    
    login_data = {
        "username": username,
        "password": password
    }
    
    response = client._make_request(
        method="POST",
        endpoint="/login",
        data=login_data,  # Form data for login endpoint
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    token_data = response.json()
    return token_data["access_token"]


if __name__ == "__main__":
    # Example usage
    try:
        # Register a new user
        print("Registering user...")
        user = register_user("test_user", "test_password")
        print(f"User registered: {user}")
        
        # Login to get access token
        print("\nLogging in...")
        token = login_user("test_user", "test_password")
        print("Login successful!")
        
        # Fetch polls
        print("\nFetching polls...")
        polls = fetch_paginated_polls(skip=0, limit=5)
        print(f"Found {len(polls)} polls")
        
        if polls:
            poll_id = polls[0]["id"]
            
            # Get poll results
            print(f"\nGetting results for poll {poll_id}...")
            results = get_poll_results(poll_id)
            print(f"Poll results: {results}")
            
            # Cast a vote (if there are options available)
            if results["results"]:
                option_id = results["results"][0]["option_id"]
                print(f"\nCasting vote for option {option_id}...")
                vote = cast_vote(poll_id, option_id, token)
                print(f"Vote cast: {vote}")
        
    except PollyAPIError as e:
        print(f"API Error: {e.message}")
        if e.status_code:
            print(f"Status Code: {e.status_code}")
        if e.response_data:
            print(f"Response Data: {e.response_data}")
    except Exception as e:
        print(f"Unexpected error: {e}")
