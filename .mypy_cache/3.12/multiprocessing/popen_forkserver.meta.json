{"data_mtime": 1757701351, "dep_lines": [4, 5, 1, 2, 4, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 5, 30, 30, 30], "dependencies": ["multiprocessing.popen_fork", "multiprocessing.util", "sys", "typing", "multiprocessing", "builtins", "_frozen_importlib", "abc", "types"], "hash": "b23f829b373aff646f4e8d2c4d1757dae194cf55", "id": "multiprocessing.popen_forkserver", "ignore_all": true, "interface_hash": "9bd747013017131657746cc97fe8232a7b526273", "mtime": 1741361855, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/multiprocessing/popen_forkserver.pyi", "plugin_data": null, "size": 353, "suppressed": [], "version_id": "1.15.0"}