{".class": "MypyFile", "_fullname": "decimal", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BasicContext": {".class": "SymbolTableNode", "cross_ref": "_decimal.BasicContext", "kind": "Gdef"}, "Clamped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Clamped", "name": "Clamped", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Clamped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Clamped", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Clamped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Clamped", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Container": {".class": "SymbolTableNode", "cross_ref": "typing.Container", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Context", "name": "Context", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Context", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Context", "builtins.object"], "names": {".class": "SymbolTable", "Emax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.Emax", "name": "Emax", "type": "builtins.int"}}, "Emin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.Emin", "name": "Emin", "type": "builtins.int"}}, "Etiny": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.Etiny", "name": "E<PERSON>y", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Etiny of Context", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Etop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.Etop", "name": "Etop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Etop of Context", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of Context", "ret_type": "decimal.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "decimal.Context.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "prec", "rounding", "Emin", "Emax", "capitals", "clamp", "flags", "traps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "prec", "rounding", "Emin", "Emax", "capitals", "clamp", "flags", "traps"], "arg_types": ["decimal.Context", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}], "extra_attrs": null, "type_ref": "typing.Container"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}], "extra_attrs": null, "type_ref": "typing.Container"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Context", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Context", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of Context", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Context", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Context", "values": [], "variance": 0}]}}}, "abs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.abs", "name": "abs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "abs of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.canonical", "name": "canonical", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canonical of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "capitals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.capitals", "name": "capitals", "type": "builtins.int"}}, "clamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.clamp", "name": "clamp", "type": "builtins.int"}}, "clear_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.clear_flags", "name": "clear_flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_flags of Context", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_traps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.clear_traps", "name": "clear_traps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_traps of Context", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.compare", "name": "compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.compare_signal", "name": "compare_signal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_signal of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_total": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.compare_total", "name": "compare_total", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_total of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_total_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.compare_total_mag", "name": "compare_total_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_total_mag of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Context", "ret_type": "decimal.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_abs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.copy_abs", "name": "copy_abs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_abs of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.copy_decimal", "name": "copy_decimal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_decimal of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_negate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.copy_negate", "name": "copy_negate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_negate of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.copy_sign", "name": "copy_sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_sign of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.create_decimal", "name": "create_decimal", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._DecimalNew"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decimal of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_decimal_from_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.create_decimal_from_float", "name": "create_decimal_from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decimal_from_float of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divide_int": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.divide_int", "name": "divide_int", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide_int of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divmod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.divmod", "name": "divmod", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divmod of Context", "ret_type": {".class": "TupleType", "implicit": false, "items": ["decimal.Decimal", "decimal.Decimal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.exp", "name": "exp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exp of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.flags", "name": "flags", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.fma", "name": "fma", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fma of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_canonical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_canonical", "name": "is_canonical", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_canonical of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_finite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_finite", "name": "is_finite", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_finite of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_infinite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_infinite", "name": "is_infinite", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_infinite of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_nan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_nan", "name": "is_nan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_nan of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_normal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_normal", "name": "is_normal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_normal of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_qnan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_qnan", "name": "is_qnan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_qnan of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_signed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_signed", "name": "is_signed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_signed of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_snan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_snan", "name": "is_snan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_snan of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_subnormal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_subnormal", "name": "is_subnormal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_subnormal of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.is_zero", "name": "is_zero", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_zero of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ln": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.ln", "name": "ln", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ln of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log10": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.log10", "name": "log10", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log10 of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.logb", "name": "logb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logb of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_and": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.logical_and", "name": "logical_and", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_and of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_invert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.logical_invert", "name": "logical_invert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_invert of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.logical_or", "name": "logical_or", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_or of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_xor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.logical_xor", "name": "logical_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_xor of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.max", "name": "max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.max_mag", "name": "max_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_mag of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "min": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.min", "name": "min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "min_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.min_mag", "name": "min_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min_mag of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "minus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.minus", "name": "minus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "minus of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multiply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.multiply", "name": "multiply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_minus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.next_minus", "name": "next_minus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_minus of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.next_plus", "name": "next_plus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_plus of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_toward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.next_toward", "name": "next_toward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_toward of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "number_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.number_class", "name": "number_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "number_class of Context", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.plus", "name": "plus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plus of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "power": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "a", "b", "modulo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.power", "name": "power", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "a", "b", "modulo"], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "power of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.prec", "name": "prec", "type": "builtins.int"}}, "quantize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.quantize", "name": "quantize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantize of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.radix", "name": "radix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "radix of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remainder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.remainder", "name": "remainder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remainder of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remainder_near": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.remainder_near", "name": "remainder_near", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remainder_near of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.rotate", "name": "rotate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotate of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rounding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.rounding", "name": "rounding", "type": "builtins.str"}}, "same_quantum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.same_quantum", "name": "same_quantum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "same_quantum of Context", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scaleb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.scaleb", "name": "scaleb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scaleb of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.shift", "name": "shift", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shift of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqrt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.sqrt", "name": "sqrt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqrt of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subtract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.subtract", "name": "subtract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subtract of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_eng_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.to_eng_string", "name": "to_eng_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_eng_string of Context", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.to_integral", "name": "to_integral", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral_exact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.to_integral_exact", "name": "to_integral_exact", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral_exact of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.to_integral_value", "name": "to_integral_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral_value of Context", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_sci_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Context.to_sci_string", "name": "to_sci_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Context", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_sci_string of Context", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.Context.traps", "name": "traps", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Context", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConversionSyntax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.InvalidOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.ConversionSyntax", "name": "ConversionSyntax", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.ConversionSyntax", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.ConversionSyntax", "decimal.InvalidOperation", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.ConversionSyntax.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.ConversionSyntax", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Decimal", "name": "Decimal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Decimal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Decimal", "builtins.object"], "names": {".class": "SymbolTable", "__abs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__abs__", "name": "__abs__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__abs__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ceil__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__ceil__", "name": "__ceil__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ceil__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__complex__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__complex__", "name": "__complex__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__complex__ of Decimal", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of Decimal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of Decimal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}}, "__divmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Decimal", "ret_type": {".class": "TupleType", "implicit": false, "items": ["decimal.Decimal", "decimal.Decimal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__float__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__float__", "name": "__float__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__float__ of Decimal", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__floor__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__floor__", "name": "__floor__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floor__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__floordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__format__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__format__", "name": "__format__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["decimal.Decimal", "builtins.str", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__format__ of Decimal", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__int__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__int__", "name": "__int__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__int__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__neg__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__neg__", "name": "__neg__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__neg__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "value", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "decimal.Decimal.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "value", "context"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._DecimalNew"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Decimal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}}, "__pos__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__pos__", "name": "__pos__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pos__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__pow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__radd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rdivmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Decimal", "ret_type": {".class": "TupleType", "implicit": false, "items": ["decimal.Decimal", "decimal.Decimal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of Decimal", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}}, "__rfloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__round__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__round__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "decimal.Decimal.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "decimal.Decimal.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "decimal.Decimal.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "decimal.Decimal.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rpow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rpow__", "name": "__rpow__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rsub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rtruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__truediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__trunc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.__trunc__", "name": "__trunc__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__trunc__ of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjusted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.adjusted", "name": "adjusted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjusted of Decimal", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_integer_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.as_integer_ratio", "name": "as_integer_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_integer_ratio of Decimal", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.as_tuple", "name": "as_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_tuple of Decimal", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "decimal.DecimalTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.canonical", "name": "canonical", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canonical of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.compare", "name": "compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.compare_signal", "name": "compare_signal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_signal of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_total": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.compare_total", "name": "compare_total", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_total of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_total_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.compare_total_mag", "name": "compare_total_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_total_mag of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conjugate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.conjugate", "name": "conjugate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conjugate of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_abs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.copy_abs", "name": "copy_abs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_abs of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_negate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.copy_negate", "name": "copy_negate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_negate of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.copy_sign", "name": "copy_sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_sign of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.exp", "name": "exp", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exp of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "other", "third", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.fma", "name": "fma", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "other", "third", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fma of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "decimal.Decimal.from_float", "name": "from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_float of Decimal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "decimal.Decimal.from_float", "name": "from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_float of Decimal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}]}}}}, "imag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "decimal.Decimal.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "decimal.Decimal.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_canonical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_canonical", "name": "is_canonical", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_canonical of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_finite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_finite", "name": "is_finite", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_finite of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_infinite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_infinite", "name": "is_infinite", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_infinite of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_nan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_nan", "name": "is_nan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_nan of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_normal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_normal", "name": "is_normal", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_normal of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_qnan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_qnan", "name": "is_qnan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_qnan of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_signed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_signed", "name": "is_signed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_signed of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_snan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_snan", "name": "is_snan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_snan of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_subnormal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_subnormal", "name": "is_subnormal", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_subnormal of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.is_zero", "name": "is_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_zero of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ln": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.ln", "name": "ln", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ln of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log10": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.log10", "name": "log10", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log10 of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.logb", "name": "logb", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logb of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_and": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.logical_and", "name": "logical_and", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_and of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_invert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.logical_invert", "name": "logical_invert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_invert of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.logical_or", "name": "logical_or", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_or of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logical_xor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.logical_xor", "name": "logical_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_xor of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.max", "name": "max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.max_mag", "name": "max_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_mag of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "min": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.min", "name": "min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "min_mag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.min_mag", "name": "min_mag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min_mag of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_minus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.next_minus", "name": "next_minus", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_minus of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.next_plus", "name": "next_plus", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_plus of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_toward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.next_toward", "name": "next_toward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_toward of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "number_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.number_class", "name": "number_class", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "number_class of Decimal", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quantize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "exp", "rounding", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.quantize", "name": "quantize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "exp", "rounding", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantize of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.radix", "name": "radix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "radix of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "decimal.Decimal.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "decimal.Decimal.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "remainder_near": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.remainder_near", "name": "remainder_near", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remainder_near of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.rotate", "name": "rotate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotate of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "same_quantum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.same_quantum", "name": "same_quantum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "same_quantum of Decimal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scaleb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.scaleb", "name": "scaleb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scaleb of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.shift", "name": "shift", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "context"], "arg_types": ["decimal.Decimal", {".class": "TypeAliasType", "args": [], "type_ref": "decimal._Decimal"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shift of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqrt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.sqrt", "name": "sqrt", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqrt of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_eng_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.to_eng_string", "name": "to_eng_string", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_eng_string of Decimal", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.to_integral", "name": "to_integral", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral_exact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.to_integral_exact", "name": "to_integral_exact", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral_exact of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_integral_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.Decimal.to_integral_value", "name": "to_integral_value", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "rounding", "context"], "arg_types": ["decimal.Decimal", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_integral_value of Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Decimal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Decimal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DecimalException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ArithmeticError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.DecimalException", "name": "DecimalException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.DecimalException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.DecimalException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DecimalTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.DecimalTuple", "name": "DecimalTuple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "decimal.DecimalTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["sign", "digits", "exponent"]}}, "module_name": "decimal", "mro": ["decimal.DecimalTuple", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "sign"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "digits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exponent"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "sign", "digits", "exponent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "decimal.DecimalTuple.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "sign", "digits", "exponent"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of DecimalTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.DecimalTuple._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of DecimalTuple", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "decimal.DecimalTuple._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DecimalTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "decimal.DecimalTuple._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DecimalTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "sign", "digits", "exponent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal.DecimalTuple._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "sign", "digits", "exponent"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of DecimalTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple._NT", "id": -1, "name": "_NT", "namespace": "decimal.DecimalTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "decimal.DecimalTuple._source", "name": "_source", "type": "builtins.str"}}, "digits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "decimal.DecimalTuple.digits", "name": "digits", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "digits-redefinition": {".class": "SymbolTableNode", "cross_ref": "decimal.DecimalTuple.digits", "kind": "<PERSON><PERSON><PERSON>"}, "exponent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "decimal.DecimalTuple.exponent", "name": "exponent", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}}}, "exponent-redefinition": {".class": "SymbolTableNode", "cross_ref": "decimal.DecimalTuple.exponent", "kind": "<PERSON><PERSON><PERSON>"}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "decimal.DecimalTuple.sign", "name": "sign", "type": "builtins.int"}}, "sign-redefinition": {".class": "SymbolTableNode", "cross_ref": "decimal.DecimalTuple.sign", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DecimalTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": "decimal.DecimalTuple"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "DefaultContext": {".class": "SymbolTableNode", "cross_ref": "_decimal.DefaultContext", "kind": "Gdef"}, "DivisionByZero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException", "builtins.ZeroDivisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.DivisionByZero", "name": "DivisionByZero", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.DivisionByZero", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.DivisionByZero", "decimal.DecimalException", "builtins.ZeroDivisionError", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DivisionByZero.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.DivisionByZero", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DivisionImpossible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.InvalidOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.DivisionImpossible", "name": "DivisionImpossible", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.DivisionImpossible", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.DivisionImpossible", "decimal.InvalidOperation", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DivisionImpossible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.DivisionImpossible", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DivisionUndefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.InvalidOperation", "builtins.ZeroDivisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.DivisionUndefined", "name": "DivisionUndefined", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.DivisionUndefined", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.DivisionUndefined", "decimal.InvalidOperation", "decimal.DecimalException", "builtins.ZeroDivisionError", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.DivisionUndefined.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.DivisionUndefined", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtendedContext": {".class": "SymbolTableNode", "cross_ref": "_decimal.ExtendedContext", "kind": "Gdef"}, "FloatOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException", "builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.FloatOperation", "name": "FloatOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.FloatOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.FloatOperation", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.FloatOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.FloatOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HAVE_CONTEXTVAR": {".class": "SymbolTableNode", "cross_ref": "_decimal.HAVE_CONTEXTVAR", "kind": "Gdef"}, "HAVE_THREADS": {".class": "SymbolTableNode", "cross_ref": "_decimal.HAVE_THREADS", "kind": "Gdef"}, "Inexact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Inexact", "name": "Inexact", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Inexact", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Inexact", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Inexact.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Inexact", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.InvalidOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.InvalidContext", "name": "InvalidContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.InvalidContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.InvalidContext", "decimal.InvalidOperation", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.InvalidContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.InvalidContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.InvalidOperation", "name": "InvalidOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.InvalidOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.InvalidOperation", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.InvalidOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.InvalidOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_EMAX": {".class": "SymbolTableNode", "cross_ref": "_decimal.MAX_EMAX", "kind": "Gdef"}, "MAX_PREC": {".class": "SymbolTableNode", "cross_ref": "_decimal.MAX_PREC", "kind": "Gdef"}, "MIN_EMIN": {".class": "SymbolTableNode", "cross_ref": "_decimal.MIN_EMIN", "kind": "Gdef"}, "MIN_ETINY": {".class": "SymbolTableNode", "cross_ref": "_decimal.MIN_ETINY", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Overflow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.Inexact", "decimal.Rounded"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Overflow", "name": "Overflow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Overflow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Overflow", "decimal.Inexact", "decimal.Rounded", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Overflow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Overflow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROUND_05UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_05UP", "kind": "Gdef"}, "ROUND_CEILING": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_CEILING", "kind": "Gdef"}, "ROUND_DOWN": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_DOWN", "kind": "Gdef"}, "ROUND_FLOOR": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_FLOOR", "kind": "Gdef"}, "ROUND_HALF_DOWN": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_DOWN", "kind": "Gdef"}, "ROUND_HALF_EVEN": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_EVEN", "kind": "Gdef"}, "ROUND_HALF_UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_UP", "kind": "Gdef"}, "ROUND_UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_UP", "kind": "Gdef"}, "Rounded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Rounded", "name": "Rounded", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Rounded", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Rounded", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Rounded.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Rounded", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Subnormal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.DecimalException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Subnormal", "name": "Subnormal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Subnormal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Subnormal", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Subnormal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Subnormal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Underflow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["decimal.Inexact", "decimal.Rounded", "decimal.Subnormal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal.Underflow", "name": "Underflow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "decimal.Underflow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal.Underflow", "decimal.Inexact", "decimal.Rounded", "decimal.Subnormal", "decimal.DecimalException", "builtins.ArithmeticError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal.Underflow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal.Underflow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ComparableNum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "decimal._ComparableNum", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "numbers.Rational"], "uses_pep604_syntax": true}}}, "_ContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "decimal._ContextManager", "name": "_ContextManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "decimal._ContextManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "decimal", "mro": ["decimal._ContextManager", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal._ContextManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal._ContextManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _ContextManager", "ret_type": "decimal.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal._ContextManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["decimal._ContextManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of _ContextManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "decimal._ContextManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_context"], "arg_types": ["decimal._ContextManager", "decimal.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ContextManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "decimal._ContextManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "decimal._ContextManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Decimal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "decimal._Decimal", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.int"], "uses_pep604_syntax": true}}}, "_DecimalNew": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "decimal._DecimalNew", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "_TrapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "decimal._TrapType", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeType", "item": "decimal.DecimalException"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__file__", "name": "__file__", "type": "builtins.str"}}, "__libmpdec_version__": {".class": "SymbolTableNode", "cross_ref": "_decimal.__libmpdec_version__", "kind": "Gdef"}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "decimal.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "_decimal.__version__", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "getcontext": {".class": "SymbolTableNode", "cross_ref": "_decimal.getcontext", "kind": "Gdef"}, "localcontext": {".class": "SymbolTableNode", "cross_ref": "_decimal.localcontext", "kind": "Gdef"}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setcontext": {".class": "SymbolTableNode", "cross_ref": "_decimal.setcontext", "kind": "Gdef"}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/root/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/decimal.pyi"}