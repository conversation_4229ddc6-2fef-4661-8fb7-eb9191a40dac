#!/usr/bin/env python3
"""
Test script for the Polly API client functions.

This script tests the client functions against a running Polly API server.
Make sure the server is running on http://localhost:8000 before running this script.
"""

import unittest
from unittest.mock import patch, <PERSON>ck
import json
from polly_client import (
    register_user, 
    login_user, 
    fetch_paginated_polls, 
    cast_vote, 
    get_poll_results,
    PollyAPIError,
    PollyClient
)


class TestPollyClient(unittest.TestCase):
    """Test cases for the Polly API client functions."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:8000"
        self.test_username = "test_user_123"
        self.test_password = "test_password_123"
        self.mock_token = "mock_jwt_token_12345"
    
    @patch('polly_client.requests.Session.request')
    def test_register_user_success(self, mock_request):
        """Test successful user registration."""
        # Mock successful response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "id": 1,
            "username": self.test_username
        }
        mock_request.return_value = mock_response
        
        # Test the function
        result = register_user(self.test_username, self.test_password, self.base_url)
        
        # Assertions
        self.assertEqual(result["id"], 1)
        self.assertEqual(result["username"], self.test_username)
        
        # Verify the request was made correctly
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], "POST")
        self.assertEqual(args[1], f"{self.base_url}/register")
        self.assertEqual(kwargs["json"]["username"], self.test_username)
        self.assertEqual(kwargs["json"]["password"], self.test_password)
    
    @patch('polly_client.requests.Session.request')
    def test_register_user_already_exists(self, mock_request):
        """Test user registration when username already exists."""
        # Mock error response
        mock_response = Mock()
        mock_response.ok = False
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "detail": "Username already registered"
        }
        mock_request.return_value = mock_response
        
        # Test the function should raise an exception
        with self.assertRaises(PollyAPIError) as context:
            register_user(self.test_username, self.test_password, self.base_url)
        
        self.assertEqual(context.exception.status_code, 400)
        self.assertIn("already registered", context.exception.message)
    
    @patch('polly_client.requests.Session.request')
    def test_fetch_paginated_polls_success(self, mock_request):
        """Test successful poll fetching with pagination."""
        # Mock successful response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = [
            {
                "id": 1,
                "question": "Test poll 1?",
                "created_at": "2024-01-01T12:00:00Z",
                "owner_id": 1,
                "options": [
                    {"id": 1, "text": "Option 1", "poll_id": 1},
                    {"id": 2, "text": "Option 2", "poll_id": 1}
                ]
            },
            {
                "id": 2,
                "question": "Test poll 2?",
                "created_at": "2024-01-01T13:00:00Z",
                "owner_id": 1,
                "options": [
                    {"id": 3, "text": "Yes", "poll_id": 2},
                    {"id": 4, "text": "No", "poll_id": 2}
                ]
            }
        ]
        mock_request.return_value = mock_response
        
        # Test the function
        result = fetch_paginated_polls(skip=0, limit=10, base_url=self.base_url)
        
        # Assertions
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["id"], 1)
        self.assertEqual(result[0]["question"], "Test poll 1?")
        self.assertEqual(len(result[0]["options"]), 2)
        
        # Verify the request was made correctly
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], "GET")
        self.assertEqual(args[1], f"{self.base_url}/polls")
        self.assertEqual(kwargs["params"]["skip"], 0)
        self.assertEqual(kwargs["params"]["limit"], 10)
    
    @patch('polly_client.requests.Session.request')
    def test_cast_vote_success(self, mock_request):
        """Test successful vote casting."""
        # Mock successful response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "id": 1,
            "user_id": 1,
            "option_id": 2,
            "created_at": "2024-01-01T14:00:00Z"
        }
        mock_request.return_value = mock_response
        
        # Test the function
        result = cast_vote(poll_id=1, option_id=2, access_token=self.mock_token, base_url=self.base_url)
        
        # Assertions
        self.assertEqual(result["id"], 1)
        self.assertEqual(result["user_id"], 1)
        self.assertEqual(result["option_id"], 2)
        
        # Verify the request was made correctly
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], "POST")
        self.assertEqual(args[1], f"{self.base_url}/polls/1/vote")
        self.assertEqual(kwargs["json"]["option_id"], 2)
        self.assertIn("Authorization", kwargs["headers"])
        self.assertEqual(kwargs["headers"]["Authorization"], f"Bearer {self.mock_token}")
    
    @patch('polly_client.requests.Session.request')
    def test_cast_vote_unauthorized(self, mock_request):
        """Test vote casting with invalid token."""
        # Mock unauthorized response
        mock_response = Mock()
        mock_response.ok = False
        mock_response.status_code = 401
        mock_response.json.return_value = {
            "detail": "Unauthorized"
        }
        mock_request.return_value = mock_response
        
        # Test the function should raise an exception
        with self.assertRaises(PollyAPIError) as context:
            cast_vote(poll_id=1, option_id=2, access_token="invalid_token", base_url=self.base_url)
        
        self.assertEqual(context.exception.status_code, 401)
        self.assertIn("Unauthorized", context.exception.message)
    
    @patch('polly_client.requests.Session.request')
    def test_get_poll_results_success(self, mock_request):
        """Test successful poll results retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.ok = True
        mock_response.json.return_value = {
            "poll_id": 1,
            "question": "Test poll?",
            "results": [
                {"option_id": 1, "text": "Option 1", "vote_count": 5},
                {"option_id": 2, "text": "Option 2", "vote_count": 3}
            ]
        }
        mock_request.return_value = mock_response
        
        # Test the function
        result = get_poll_results(poll_id=1, base_url=self.base_url)
        
        # Assertions
        self.assertEqual(result["poll_id"], 1)
        self.assertEqual(result["question"], "Test poll?")
        self.assertEqual(len(result["results"]), 2)
        self.assertEqual(result["results"][0]["vote_count"], 5)
        self.assertEqual(result["results"][1]["vote_count"], 3)
        
        # Verify the request was made correctly
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args
        self.assertEqual(args[0], "GET")
        self.assertEqual(args[1], f"{self.base_url}/polls/1/results")
    
    @patch('polly_client.requests.Session.request')
    def test_get_poll_results_not_found(self, mock_request):
        """Test poll results retrieval for non-existent poll."""
        # Mock not found response
        mock_response = Mock()
        mock_response.ok = False
        mock_response.status_code = 404
        mock_response.json.return_value = {
            "detail": "Poll not found"
        }
        mock_request.return_value = mock_response
        
        # Test the function should raise an exception
        with self.assertRaises(PollyAPIError) as context:
            get_poll_results(poll_id=999, base_url=self.base_url)
        
        self.assertEqual(context.exception.status_code, 404)
        self.assertIn("Poll not found", context.exception.message)
    
    def test_polly_client_initialization(self):
        """Test PollyClient initialization."""
        client = PollyClient("http://example.com", timeout=60)
        
        self.assertEqual(client.base_url, "http://example.com")
        self.assertEqual(client.timeout, 60)
        self.assertIsNone(client.access_token)
        
        # Test token setting
        client.set_access_token("test_token")
        self.assertEqual(client.access_token, "test_token")


def run_integration_tests():
    """
    Run integration tests against a live server.
    
    Note: This requires a running Polly API server on localhost:8000
    """
    print("Running integration tests...")
    print("Make sure the Polly API server is running on http://localhost:8000")
    
    try:
        # Test basic connectivity
        polls = fetch_paginated_polls(skip=0, limit=1)
        print(f"✅ Server is reachable. Found {len(polls)} polls.")
        
        # You can add more integration tests here
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        print("Make sure the server is running and accessible.")


if __name__ == "__main__":
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # Run integration tests (optional)
    # Uncomment the line below to run integration tests
    # run_integration_tests()
